<?php
// 获取用户名参数
$username = isset($_GET['username']) ? trim($_GET['username']) : '';

// 商户配置
$merchant_secret = '5201314876'; // 您的商户秘钥

// 商品配置
$products = [
    84 => ['name' => '10积分', 'price' => 7.08, 'type' => 'points', 'value' => 10],
    83 => ['name' => '5积分', 'price' => 4.00, 'type' => 'points', 'value' => 5],
    82 => ['name' => '1积分', 'price' => 0.80, 'type' => 'points', 'value' => 1],
    81 => ['name' => '永久会员', 'price' => 68.99, 'type' => 'vip', 'value' => 99999],
    80 => ['name' => '半年会员', 'price' => 48.99, 'type' => 'vip', 'value' => 180],
    79 => ['name' => '月度会员', 'price' => 9.99, 'type' => 'vip', 'value' => 30],
    78 => ['name' => '一周会员', 'price' => 6.99, 'type' => 'vip', 'value' => 7]
];

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_order') {
        $product_id = intval($_POST['product_id']);
        $pay_type = $_POST['pay_type'] ?? 'wxpay';
        $customer_contact = $_POST['customer_contact'] ?? $username;
        
        // 调用创建订单API
        $url = "https://cloudshop.qnm6.top/create_order.php?" . http_build_query([
            'customer_contact' => $customer_contact,
            'product_id' => $product_id,
            'pay_type' => $pay_type
        ]);
        
        $response = file_get_contents($url);
        echo $response;
        exit;
    }
    
    if ($action === 'check_payment') {
        $order_id = $_POST['order_id'];
        
        // 检查支付状态
        $url = "https://cloudshop.qnm6.top/check_payment_status.php?" . http_build_query([
            'order_id' => $order_id
        ]);
        
        $response = json_decode(file_get_contents($url), true);
        
        if ($response['status'] === 'success' && $response['data']['order_status'] === 'paid') {
            // 支付成功，进行充值
            $product_id = intval($_POST['product_id']);
            $product = $products[$product_id];
            
            if ($product['type'] === 'points') {
                // 充值积分
                $charge_url = "https://example.com/mika/points.php?" . http_build_query([
                    'username' => $username,
                    'points' => $product['value'],
                    'link' => 'abc123def456'
                ]);
            } else {
                // 充值会员
                $charge_url = "https://example.com/mika/vip.php?" . http_build_query([
                    'username' => $username,
                    'days' => $product['value'],
                    'link' => 'abc123def456'
                ]);
            }
            
            $charge_response = file_get_contents($charge_url);
            $response['charge_result'] = json_decode($charge_response, true);
        }
        
        echo json_encode($response);
        exit;
    }
}

if (empty($username)) {
    die('<div style="text-align:center;margin-top:50px;color:#ff6b35;">错误：缺少用户名参数</div>');
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品购买 - <?php echo htmlspecialchars($username); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2d3436;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .username {
            color: #74b9ff;
            font-size: 18px;
            font-weight: 500;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .product-card.selected {
            border-color: #ff6b35;
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
        }
        
        .product-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .product-price {
            font-size: 24px;
            font-weight: 700;
            color: #ff6b35;
        }
        
        .product-card.selected .product-price {
            color: white;
        }
        
        .payment-section {
            display: none;
            margin-top: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3436;
            margin-bottom: 15px;
        }
        
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .payment-method {
            flex: 1;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .payment-method:hover {
            border-color: #74b9ff;
        }
        
        .payment-method.selected {
            border-color: #ff6b35;
            background: #ff6b35;
            color: white;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
        }
        
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 53, 0.4);
        }
        
        .order-section {
            display: none;
            margin-top: 30px;
            text-align: center;
        }
        
        .order-info {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #74b9ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .products-grid {
                grid-template-columns: 1fr;
            }
            
            .payment-methods {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>商品购买</h1>
            <div class="username">用户：<?php echo htmlspecialchars($username); ?></div>
        </div>
        
        <div class="products-grid">
            <?php foreach ($products as $id => $product): ?>
            <div class="product-card" data-id="<?php echo $id; ?>">
                <div class="product-name"><?php echo htmlspecialchars($product['name']); ?></div>
                <div class="product-price">¥<?php echo number_format($product['price'], 2); ?></div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="payment-section" id="paymentSection">
            <div class="section-title">选择支付方式</div>
            <div class="payment-methods">
                <div class="payment-method" data-type="wxpay">
                    <div>💬 微信支付</div>
                </div>
                <div class="payment-method" data-type="alipay">
                    <div>💰 支付宝</div>
                </div>
            </div>
            <button class="btn btn-primary" id="createOrderBtn">确认订单</button>
        </div>
        
        <div class="order-section" id="orderSection">
            <div class="order-info" id="orderInfo"></div>
            <button class="btn btn-secondary" id="payNowBtn">立即支付</button>
            <button class="btn btn-primary" id="checkPaymentBtn">我已支付</button>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        let selectedProduct = null;
        let selectedPayment = null;
        let currentOrder = null;
        
        // 选择商品
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.product-card').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedProduct = this.dataset.id;
                document.getElementById('paymentSection').style.display = 'block';
            });
        });
        
        // 选择支付方式
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
                selectedPayment = this.dataset.type;
            });
        });
        
        // 创建订单
        document.getElementById('createOrderBtn').addEventListener('click', function() {
            if (!selectedProduct || !selectedPayment) {
                showModal('请选择商品和支付方式');
                return;
            }
            
            this.innerHTML = '<span class="loading"></span> 创建订单中...';
            this.disabled = true;
            
            const formData = new FormData();
            formData.append('action', 'create_order');
            formData.append('product_id', selectedProduct);
            formData.append('pay_type', selectedPayment);
            formData.append('customer_contact', '<?php echo $username; ?>');
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    currentOrder = data.data;
                    showOrderInfo(data.data);
                    document.getElementById('orderSection').style.display = 'block';
                } else {
                    showModal('订单创建失败：' + data.message);
                }
            })
            .catch(error => {
                showModal('网络错误，请重试');
            })
            .finally(() => {
                this.innerHTML = '确认订单';
                this.disabled = false;
            });
        });
        
        // 立即支付
        document.getElementById('payNowBtn').addEventListener('click', function() {
            if (currentOrder && currentOrder.payment_url) {
                window.open(currentOrder.payment_url, '_blank');
            }
        });
        
        // 检查支付状态
        document.getElementById('checkPaymentBtn').addEventListener('click', function() {
            if (!currentOrder) return;
            
            this.innerHTML = '<span class="loading"></span> 检查中...';
            this.disabled = true;
            
            const formData = new FormData();
            formData.append('action', 'check_payment');
            formData.append('order_id', currentOrder.order_info.order_id);
            formData.append('product_id', selectedProduct);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.data.order_status === 'paid') {
                    showModal('支付成功！充值已完成', true);
                } else {
                    showModal('订单未支付，请先完成支付', false, true);
                }
            })
            .catch(error => {
                showModal('检查失败，请重试');
            })
            .finally(() => {
                this.innerHTML = '我已支付';
                this.disabled = false;
            });
        });
        
        function showOrderInfo(orderData) {
            const orderInfo = document.getElementById('orderInfo');
            orderInfo.innerHTML = `
                <div><strong>订单号：</strong>${orderData.order_info.order_id}</div>
                <div><strong>商品：</strong>${orderData.order_info.product_name}</div>
                <div><strong>金额：</strong>¥${orderData.order_info.product_price}</div>
                <div><strong>支付方式：</strong>${orderData.pay_type === 'wxpay' ? '微信支付' : '支付宝'}</div>
            `;
        }
        
        function showModal(message, isSuccess = false, showPayButton = false) {
            const modal = document.getElementById('modal');
            const content = document.getElementById('modalContent');
            
            let buttons = '<button class="btn btn-primary" onclick="closeModal()">确定</button>';
            if (showPayButton) {
                buttons = `
                    <button class="btn btn-secondary" onclick="document.getElementById('payNowBtn').click(); closeModal();">前往支付</button>
                    <button class="btn btn-primary" onclick="closeModal()">取消</button>
                `;
            }
            
            content.innerHTML = `
                <div style="margin-bottom: 20px; color: ${isSuccess ? '#00b894' : '#2d3436'};">
                    ${message}
                </div>
                ${buttons}
            `;
            
            modal.style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html>
